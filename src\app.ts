import Fastify, { FastifyInstance } from 'fastify';
import { config } from './config';
import { logger, requestLogger } from './config/logger';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import jwt from '@fastify/jwt';
import cookie from '@fastify/cookie';
import multipart from '@fastify/multipart';
import staticFiles from '@fastify/static';
import fs from 'fs';
import { healthRoutes } from './routes/health.routes';
import { authRoutes } from './routes/auth.routes';
import { userRoutes } from './routes/user.routes';
import { transferRoutes } from './routes/transfer.routes';
import { agentRoutes } from './routes/agent.routes';
import { loanRoutes } from './routes/loan.routes';
import { savingsRoutes } from './routes/savings.routes';
import { adminRoutes } from './routes/admin.routes';
import { cardRoutes } from './routes/card.routes';
import { realtimeRoutes } from './routes/realtime.routes';
import { agentDashboardRoutes } from './routes/agent-dashboard.routes';
import { searchRoutes } from './routes/search.routes';

export async function createApp(): Promise<FastifyInstance> {
  const app = Fastify({
    logger: false,
    trustProxy: true,
    bodyLimit: config.upload.maxFileSize,
    requestIdHeader: 'x-request-id',
    requestIdLogLabel: 'requestId'
  });

  await setupPlugins(app);
  setupRoutes(app);
  setupErrorHandlers(app);

  return app;
}

async function setupPlugins(app: FastifyInstance): Promise<void> {
  try {
    // Security plugins
    await app.register(helmet, {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false
    });

    await app.register(cors, {
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-API-Key']
    });

    // Rate limiting
    await app.register(rateLimit, {
      max: config.rateLimit.max,
      timeWindow: config.rateLimit.windowMs,
      skipOnError: true,
      keyGenerator: (request) => {
        return request.ip;
      },
      errorResponseBuilder: (_request, context) => {
        return {
          success: false,
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: Math.round(context.ttl / 1000)
        };
      }
    });

    // JWT
    await app.register(jwt, {
      secret: config.jwt.secret,
      sign: {
        expiresIn: config.jwt.expiresIn
      }
    });

    // Cookie support
    await app.register(cookie, {
      secret: config.jwt.secret,
      parseOptions: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      }
    });

    // File upload support
    await app.register(multipart, {
      limits: {
        fileSize: config.upload.maxFileSize,
        files: 5 // Max 5 files
      }
    });

    // Static files
    if (fs.existsSync('./public')) {
      await app.register(staticFiles, {
        root: './public',
        prefix: '/public/'
      });
    }

    // Request logging
    app.addHook('onRequest', async (request, _reply) => {
      requestLogger.info('Incoming request', {
        method: request.method,
        url: request.url,
        ip: request.ip,
        userAgent: request.headers['user-agent']
      });
    });

    // Monitoring middleware
    // app.addHook('onRequest', MonitoringMiddleware.trackRequest);
    // app.addHook('onResponse', MonitoringMiddleware.trackResponse);

    logger.info('Plugins registered successfully');
  } catch (error) {
    logger.error('Error setting up plugins:', error);
    throw error;
  }
}

function setupRoutes(app: FastifyInstance): void {
  try {
    app.register(healthRoutes, { prefix: '/api/v1/health' });
    app.register(authRoutes, { prefix: '/api/v1/auth' });
    app.register(userRoutes, { prefix: '/api/v1/user' });
    app.register(transferRoutes, { prefix: '/api/v1/transfer' });
    app.register(agentRoutes, { prefix: '/api/v1/agent' });
    app.register(loanRoutes, { prefix: '/api/v1/loan' });
    app.register(savingsRoutes, { prefix: '/api/v1/savings' });
    app.register(adminRoutes, { prefix: '/api/v1/admin' });
    app.register(cardRoutes, { prefix: '/api/v1/card' });
    app.register(realtimeRoutes, { prefix: '/api/v1/realtime' });
    app.register(agentDashboardRoutes, { prefix: '/api/v1/agent-dashboard' });
    app.register(searchRoutes, { prefix: '/api/v1/search' });

    logger.info('Routes registered successfully');
  } catch (error) {
    logger.error('Error setting up routes:', error);
    throw error;
  }
}

function setupErrorHandlers(app: FastifyInstance): void {
  // Global error handler
  app.setErrorHandler((error, request, reply) => {
    logger.error('Unhandled error:', {
      error: error.message,
      stack: error.stack,
      url: request.url,
      method: request.method,
      ip: request.ip
    });

    const statusCode = error.statusCode || 500;
    const message = statusCode === 500 ? 'Internal server error' : error.message;

    reply.status(statusCode).send({
      success: false,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
  });

  // 404 handler
  app.setNotFoundHandler((request, reply) => {
    reply.status(404).send({
      success: false,
      message: 'Route not found',
      path: request.url
    });
  });
}
