# AETRUST CI/CD PIPELINE & DEPLOYMENT STRATEGY

## 1. CI/CD PIPELINE OVERVIEW

### 1.1 Pipeline Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              CI/CD PIPELINE                                    │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   Source    │───▶│    Build    │───▶│    Test     │───▶│   Deploy    │     │
│  │   Control   │    │   & Lint    │    │   & Scan    │    │ & Monitor   │     │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │                   │          │
│         ▼                   ▼                   ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   GitHub    │    │ TypeScript  │    │ Unit Tests  │    │ Development │     │
│  │ Repository  │    │ Compilation │    │ Integration │    │ Environment │     │
│  │             │    │ ESLint      │    │ Security    │    │             │     │
│  │             │    │ Prettier    │    │ Coverage    │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                   │                   │          │
│                             ▼                   ▼                   ▼          │
│                      ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│                      │   Docker    │    │   Quality   │    │   Staging   │     │
│                      │   Build     │    │   Gates     │    │ Environment │     │
│                      └─────────────┘    └─────────────┘    └─────────────┘     │
│                             │                   │                   │          │
│                             ▼                   ▼                   ▼          │
│                      ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│                      │ Container   │    │ Performance │    │ Production  │     │
│                      │ Registry    │    │ Testing     │    │ Environment │     │
│                      └─────────────┘    └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 GitHub Actions Workflow

```yaml
name: AETrust CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: aetrust/backend

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        ports:
          - 27017:27017
      redis:
        image: redis:7.0
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Type checking
        run: npm run type-check
      
      - name: Lint code
        run: npm run lint
      
      - name: Format check
        run: npm run format:check
      
      - name: Run unit tests
        run: npm run test:unit
        env:
          NODE_ENV: test
          MONGODB_URI: mongodb://localhost:27017/aetrust_test
          REDIS_URL: redis://localhost:6379
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          NODE_ENV: test
          MONGODB_URI: mongodb://localhost:27017/aetrust_test
          REDIS_URL: redis://localhost:6379
      
      - name: Generate test coverage
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  security-scan:
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run security audit
        run: npm audit --audit-level=moderate
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
      
      - name: Run SAST with CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: typescript
      
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  build-and-push:
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          # Add deployment commands here
      
      - name: Run smoke tests
        run: |
          echo "Running smoke tests on staging"
          # Add smoke test commands here
      
      - name: Notify team
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  deploy-production:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          # Add production deployment commands here
      
      - name: Run health checks
        run: |
          echo "Running production health checks"
          # Add health check commands here
      
      - name: Notify team
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## 2. DOCKER CONFIGURATION

### 2.1 Multi-Stage Dockerfile

```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S aetrust -u 1001

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder --chown=aetrust:nodejs /app/dist ./dist

# Copy necessary files
COPY --chown=aetrust:nodejs .env.example .env

# Set security headers
RUN apk add --no-cache dumb-init

# Switch to non-root user
USER aetrust

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/health-check.js

# Start application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]
```

### 2.2 Docker Compose for Development

```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/aetrust_dev
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - aetrust-network

  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=aetrust_dev
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - aetrust-network

  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - aetrust-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - aetrust-network

volumes:
  mongodb_data:
  redis_data:

networks:
  aetrust-network:
    driver: bridge
```

## 3. KUBERNETES DEPLOYMENT

### 3.1 Kubernetes Manifests

**Namespace:**
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: aetrust-production
  labels:
    name: aetrust-production
```

**ConfigMap:**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: aetrust-config
  namespace: aetrust-production
data:
  NODE_ENV: "production"
  API_VERSION: "v1"
  LOG_LEVEL: "info"
  RATE_LIMIT_MAX: "1000"
  RATE_LIMIT_WINDOW: "900000"
```

**Secret:**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: aetrust-secrets
  namespace: aetrust-production
type: Opaque
data:
  JWT_SECRET: <base64-encoded-secret>
  MONGODB_URI: <base64-encoded-uri>
  REDIS_URL: <base64-encoded-url>
  SMTP_PASSWORD: <base64-encoded-password>
```

**Deployment:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aetrust-backend
  namespace: aetrust-production
  labels:
    app: aetrust-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: aetrust-backend
  template:
    metadata:
      labels:
        app: aetrust-backend
    spec:
      containers:
      - name: aetrust-backend
        image: ghcr.io/aetrust/backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: aetrust-config
              key: NODE_ENV
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: aetrust-secrets
              key: JWT_SECRET
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: aetrust-secrets
              key: MONGODB_URI
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

**Service:**
```yaml
apiVersion: v1
kind: Service
metadata:
  name: aetrust-backend-service
  namespace: aetrust-production
spec:
  selector:
    app: aetrust-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

**Ingress:**
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aetrust-ingress
  namespace: aetrust-production
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.aetrust.com
    secretName: aetrust-tls
  rules:
  - host: api.aetrust.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: aetrust-backend-service
            port:
              number: 80
```

## 4. MONITORING & OBSERVABILITY

### 4.1 Prometheus Configuration

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: aetrust-production
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "aetrust_rules.yml"
    
    scrape_configs:
      - job_name: 'aetrust-backend'
        static_configs:
          - targets: ['aetrust-backend-service:80']
        metrics_path: '/metrics'
        scrape_interval: 10s
      
      - job_name: 'mongodb'
        static_configs:
          - targets: ['mongodb-exporter:9216']
      
      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
```

### 4.2 Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "title": "AETrust Backend Metrics",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])",
            "legendFormat": "5xx errors/sec"
          }
        ]
      }
    ]
  }
}
```

## 5. DEPLOYMENT ENVIRONMENTS

### 5.1 Environment Configuration

**Development Environment:**
- Single instance deployment
- Local MongoDB and Redis
- Debug logging enabled
- Hot reloading for development
- Mock external services

**Staging Environment:**
- Production-like setup
- Shared database cluster
- Production logging levels
- Real external service integrations
- Performance testing enabled

**Production Environment:**
- Multi-instance deployment with load balancing
- High-availability database cluster
- Comprehensive monitoring and alerting
- Full security hardening
- Disaster recovery capabilities

### 5.2 Blue-Green Deployment Strategy

```bash
#!/bin/bash
# Blue-Green Deployment Script

NAMESPACE="aetrust-production"
NEW_VERSION=$1
CURRENT_COLOR=$(kubectl get service aetrust-backend-service -n $NAMESPACE -o jsonpath='{.spec.selector.color}')

if [ "$CURRENT_COLOR" = "blue" ]; then
    NEW_COLOR="green"
else
    NEW_COLOR="blue"
fi

echo "Deploying version $NEW_VERSION to $NEW_COLOR environment"

# Update deployment with new version
kubectl set image deployment/aetrust-backend-$NEW_COLOR \
    aetrust-backend=ghcr.io/aetrust/backend:$NEW_VERSION \
    -n $NAMESPACE

# Wait for rollout to complete
kubectl rollout status deployment/aetrust-backend-$NEW_COLOR -n $NAMESPACE

# Run health checks
echo "Running health checks..."
kubectl run health-check --rm -i --restart=Never \
    --image=curlimages/curl \
    -- curl -f http://aetrust-backend-$NEW_COLOR-service/api/v1/health

if [ $? -eq 0 ]; then
    echo "Health checks passed. Switching traffic to $NEW_COLOR"
    
    # Switch service to new color
    kubectl patch service aetrust-backend-service \
        -n $NAMESPACE \
        -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'
    
    echo "Deployment successful!"
else
    echo "Health checks failed. Rolling back..."
    kubectl rollout undo deployment/aetrust-backend-$NEW_COLOR -n $NAMESPACE
    exit 1
fi
```

---

**Document Version:** 1.0  
**Last Updated:** January 2024  
**Next Review:** February 2024  
**Classification:** Internal Use Only
